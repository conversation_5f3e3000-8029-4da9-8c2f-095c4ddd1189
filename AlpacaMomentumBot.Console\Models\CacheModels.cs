using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Alpaca.Markets;

namespace AlpacaMomentumBot.Models;

/// <summary>
/// Entity representing a cached index bar in SQLite database.
/// Stores historical index data to avoid re-downloading unchanged history from Polygon API.
/// </summary>
[Table("CachedIndexBars")]
[Index(nameof(Symbol), nameof(TimeUtc), IsUnique = true)]
public class CachedIndexBar
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Index symbol (e.g., "I:SPX", "I:VIX")
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Bar timestamp in UTC (converted from Polygon's milliseconds since epoch)
    /// </summary>
    [Required]
    public DateTime TimeUtc { get; set; }

    /// <summary>
    /// Opening price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Open { get; set; }

    /// <summary>
    /// Highest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal High { get; set; }

    /// <summary>
    /// Lowest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Low { get; set; }

    /// <summary>
    /// Closing price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Close { get; set; }

    /// <summary>
    /// Trading volume for the time period
    /// </summary>
    [Required]
    public long Volume { get; set; }

    /// <summary>
    /// When this record was cached (for cache management)
    /// </summary>
    [Required]
    public DateTime CachedAt { get; set; }

    /// <summary>
    /// Converts this cached entity to an IndexBar record
    /// </summary>
    public Services.IndexBar ToIndexBar()
    {
        return new Services.IndexBar(TimeUtc, Open, High, Low, Close, Volume);
    }

    /// <summary>
    /// Creates a CachedIndexBar from an IndexBar and symbol
    /// </summary>
    public static CachedIndexBar FromIndexBar(string symbol, Services.IndexBar indexBar)
    {
        return new CachedIndexBar
        {
            Symbol = symbol,
            TimeUtc = indexBar.TimeUtc,
            Open = indexBar.Open,
            High = indexBar.High,
            Low = indexBar.Low,
            Close = indexBar.Close,
            Volume = indexBar.Volume,
            CachedAt = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Metadata about cached data for each symbol to track cache freshness
/// </summary>
[Table("CacheMetadata")]
public class CacheMetadata
{
    [Key]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Latest date for which we have cached data
    /// </summary>
    [Required]
    public DateTime LatestDataDate { get; set; }

    /// <summary>
    /// When the cache was last updated
    /// </summary>
    [Required]
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Total number of cached bars for this symbol
    /// </summary>
    [Required]
    public int BarCount { get; set; }
}

/// <summary>
/// Entity representing a cached stock/ETF bar in SQLite database.
/// Stores historical stock data to avoid re-downloading unchanged history from Alpaca/Polygon APIs.
/// Supports both daily and minute timeframes with 1-year retention.
/// </summary>
[Table("CachedStockBars")]
[Index(nameof(Symbol), nameof(TimeFrame), nameof(TimeUtc), IsUnique = true)]
public class CachedStockBar
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Stock/ETF symbol (e.g., "AAPL", "SPY")
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Timeframe: "Day" or "Minute"
    /// </summary>
    [Required]
    [MaxLength(10)]
    public string TimeFrame { get; set; } = string.Empty;

    /// <summary>
    /// Bar timestamp in UTC (consistent with Alpaca format)
    /// </summary>
    [Required]
    public DateTime TimeUtc { get; set; }

    /// <summary>
    /// Opening price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Open { get; set; }

    /// <summary>
    /// Highest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal High { get; set; }

    /// <summary>
    /// Lowest price during the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Low { get; set; }

    /// <summary>
    /// Closing price for the time period
    /// </summary>
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Close { get; set; }

    /// <summary>
    /// Trading volume for the time period
    /// </summary>
    [Required]
    public long Volume { get; set; }

    /// <summary>
    /// Volume-weighted average price (if available)
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? Vwap { get; set; }

    /// <summary>
    /// Number of trades (if available)
    /// </summary>
    public ulong? TradeCount { get; set; }

    /// <summary>
    /// When this record was cached (for cache management)
    /// </summary>
    [Required]
    public DateTime CachedAt { get; set; }

    /// <summary>
    /// Converts this cached entity to an IBar interface
    /// </summary>
    public CachedBarWrapper ToIBar()
    {
        return new CachedBarWrapper(this);
    }

    /// <summary>
    /// Creates a CachedStockBar from an IBar and metadata
    /// </summary>
    public static CachedStockBar FromIBar(string symbol, string timeFrame, IBar bar)
    {
        return new CachedStockBar
        {
            Symbol = symbol,
            TimeFrame = timeFrame,
            TimeUtc = bar.TimeUtc,
            Open = bar.Open,
            High = bar.High,
            Low = bar.Low,
            Close = bar.Close,
            Volume = (long)bar.Volume,
            Vwap = bar.Vwap > 0 ? bar.Vwap : null,
            TradeCount = bar.TradeCount > 0 ? bar.TradeCount : null,
            CachedAt = DateTime.UtcNow
        };
    }
}

/// <summary>
/// Wrapper class that implements IBar interface for cached stock data compatibility.
/// Ensures cached bars can be used interchangeably with live API bars.
/// </summary>
public class CachedBarWrapper : IBar
{
    private readonly CachedStockBar _cachedBar;

    public CachedBarWrapper(CachedStockBar cachedBar)
    {
        _cachedBar = cachedBar;
    }

    public string Symbol => _cachedBar.Symbol;
    public DateTime TimeUtc => _cachedBar.TimeUtc;
    public decimal Open => _cachedBar.Open;
    public decimal High => _cachedBar.High;
    public decimal Low => _cachedBar.Low;
    public decimal Close => _cachedBar.Close;
    public decimal Volume => _cachedBar.Volume;
    public decimal Vwap => _cachedBar.Vwap ?? 0;
    public ulong TradeCount => _cachedBar.TradeCount ?? 0;
}

/// <summary>
/// Metadata about cached stock data for each symbol and timeframe to track cache freshness
/// </summary>
[Table("StockCacheMetadata")]
public class StockCacheMetadata
{
    [Key]
    [MaxLength(50)]
    public string CacheKey { get; set; } = string.Empty; // Format: "SYMBOL_TIMEFRAME" (e.g., "AAPL_Day")

    /// <summary>
    /// Stock/ETF symbol
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Timeframe: "Day" or "Minute"
    /// </summary>
    [Required]
    [MaxLength(10)]
    public string TimeFrame { get; set; } = string.Empty;

    /// <summary>
    /// Latest date for which we have cached data
    /// </summary>
    [Required]
    public DateTime LatestDataDate { get; set; }

    /// <summary>
    /// Earliest date for which we have cached data
    /// </summary>
    [Required]
    public DateTime EarliestDataDate { get; set; }

    /// <summary>
    /// When the cache was last updated
    /// </summary>
    [Required]
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Total number of bars cached for this symbol and timeframe
    /// </summary>
    [Required]
    public int BarCount { get; set; }

    /// <summary>
    /// Creates a cache key from symbol and timeframe
    /// </summary>
    public static string CreateCacheKey(string symbol, string timeFrame)
    {
        return $"{symbol}_{timeFrame}";
    }
}
