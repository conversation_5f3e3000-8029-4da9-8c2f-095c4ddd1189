using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using AlpacaMomentumBot.Services;
using AlpacaMomentumBot.Data;
using AlpacaMomentumBot.Models;
using Alpaca.Markets;

namespace AlpacaMomentumBot.Tests.Services;

public class StockBarCacheServiceTests : IDisposable
{
    private readonly StockBarCacheDbContext _dbContext;
    private readonly Mock<ILogger<StockBarCacheService>> _mockLogger;
    private readonly StockBarCacheService _cacheService;

    public StockBarCacheServiceTests()
    {
        // Use in-memory database for testing
        var options = new DbContextOptionsBuilder<StockBarCacheDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _dbContext = new StockBarCacheDbContext(options);
        _mockLogger = new Mock<ILogger<StockBarCacheService>>();
        _cacheService = new StockBarCacheService(_dbContext, _mockLogger.Object);
    }

    public void Dispose()
    {
        _dbContext.Dispose();
    }

    [Fact]
    public async Task InitializeCacheAsync_ShouldCreateDatabase()
    {
        // Act
        await _cacheService.InitializeCacheAsync();

        // Assert
        _dbContext.Database.CanConnect().Should().BeTrue();
    }

    [Fact]
    public async Task CacheBarsAsync_ShouldStoreBarsInDatabase()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = new List<IBar>
        {
            CreateMockBar("AAPL", new DateTime(2024, 1, 1), 150m, 155m, 148m, 152m, 1000000),
            CreateMockBar("AAPL", new DateTime(2024, 1, 2), 152m, 158m, 150m, 156m, 1100000)
        };

        // Act
        await _cacheService.CacheBarsAsync(symbol, timeFrame, bars);

        // Assert
        var cachedBars = await _dbContext.CachedStockBars.Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame).ToListAsync();
        cachedBars.Should().HaveCount(2);
        cachedBars[0].Close.Should().Be(152m);
        cachedBars[1].Close.Should().Be(156m);

        var cacheKey = StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await _dbContext.StockCacheMetadata.FindAsync(cacheKey);
        metadata.Should().NotBeNull();
        metadata!.BarCount.Should().Be(2);
        metadata.LatestDataDate.Should().Be(new DateTime(2024, 1, 2));
        metadata.EarliestDataDate.Should().Be(new DateTime(2024, 1, 1));
    }

    [Fact]
    public async Task GetCachedBarsAsync_ShouldReturnBarsInDateRange()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = new List<IBar>
        {
            CreateMockBar("AAPL", new DateTime(2024, 1, 1), 150m, 155m, 148m, 152m, 1000000),
            CreateMockBar("AAPL", new DateTime(2024, 1, 2), 152m, 158m, 150m, 156m, 1100000),
            CreateMockBar("AAPL", new DateTime(2024, 1, 3), 156m, 160m, 154m, 158m, 1200000)
        };

        await _cacheService.CacheBarsAsync(symbol, timeFrame, bars);

        // Act
        var result = await _cacheService.GetCachedBarsAsync(symbol, timeFrame, new DateTime(2024, 1, 1), new DateTime(2024, 1, 2));

        // Assert
        result.Should().HaveCount(2);
        result.First().TimeUtc.Should().Be(new DateTime(2024, 1, 1));
        result.Last().TimeUtc.Should().Be(new DateTime(2024, 1, 2));
    }

    [Fact]
    public async Task GetMissingDateRangeAsync_WhenNoCachedData_ShouldReturnEntireRange()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var startDate = new DateTime(2024, 1, 1);
        var endDate = new DateTime(2024, 1, 10);

        // Act
        var result = await _cacheService.GetMissingDateRangeAsync(symbol, timeFrame, startDate, endDate);

        // Assert
        result.Should().NotBeNull();
        result!.Value.startDate.Should().Be(startDate);
        result.Value.endDate.Should().Be(endDate);
    }

    [Fact]
    public async Task GetMissingDateRangeAsync_WhenCacheCoversRange_ShouldReturnNull()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = new List<IBar>
        {
            CreateMockBar("AAPL", new DateTime(2024, 1, 1), 150m, 155m, 148m, 152m, 1000000),
            CreateMockBar("AAPL", new DateTime(2024, 1, 2), 152m, 158m, 150m, 156m, 1100000),
            CreateMockBar("AAPL", new DateTime(2024, 1, 3), 156m, 160m, 154m, 158m, 1200000)
        };

        await _cacheService.CacheBarsAsync(symbol, timeFrame, bars);

        // Act
        var result = await _cacheService.GetMissingDateRangeAsync(symbol, timeFrame, new DateTime(2024, 1, 1), new DateTime(2024, 1, 3));

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetMissingDateRangeAsync_WhenPartialCache_ShouldReturnMissingRange()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = new List<IBar>
        {
            CreateMockBar("AAPL", new DateTime(2024, 1, 1), 150m, 155m, 148m, 152m, 1000000),
            CreateMockBar("AAPL", new DateTime(2024, 1, 2), 152m, 158m, 150m, 156m, 1100000)
        };

        await _cacheService.CacheBarsAsync(symbol, timeFrame, bars);

        // Act - Request data beyond what's cached
        var result = await _cacheService.GetMissingDateRangeAsync(symbol, timeFrame, new DateTime(2024, 1, 1), new DateTime(2024, 1, 5));

        // Assert
        result.Should().NotBeNull();
        result!.Value.startDate.Should().Be(new DateTime(2024, 1, 3)); // Day after latest cached
        result.Value.endDate.Should().Be(new DateTime(2024, 1, 5));
    }

    [Fact]
    public async Task IsCacheFreshAsync_WhenCacheIsUpToDate_ShouldReturnTrue()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = new List<IBar>
        {
            CreateMockBar("AAPL", new DateTime(2024, 1, 1), 150m, 155m, 148m, 152m, 1000000),
            CreateMockBar("AAPL", new DateTime(2024, 1, 2), 152m, 158m, 150m, 156m, 1100000)
        };

        await _cacheService.CacheBarsAsync(symbol, timeFrame, bars);

        // Act
        var result = await _cacheService.IsCacheFreshAsync(symbol, timeFrame, new DateTime(2024, 1, 2));

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsCacheFreshAsync_WhenCacheIsOutdated_ShouldReturnFalse()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = new List<IBar>
        {
            CreateMockBar("AAPL", new DateTime(2024, 1, 1), 150m, 155m, 148m, 152m, 1000000)
        };

        await _cacheService.CacheBarsAsync(symbol, timeFrame, bars);

        // Act
        var result = await _cacheService.IsCacheFreshAsync(symbol, timeFrame, new DateTime(2024, 1, 5));

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task PerformMaintenanceAsync_ShouldRemoveOldData()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var oldDate = DateTime.UtcNow.AddDays(-400); // Older than retention period
        var recentDate = DateTime.UtcNow.AddDays(-10); // Within retention period

        var bars = new List<IBar>
        {
            CreateMockBar("AAPL", oldDate, 150m, 155m, 148m, 152m, 1000000),
            CreateMockBar("AAPL", recentDate, 152m, 158m, 150m, 156m, 1100000)
        };

        await _cacheService.CacheBarsAsync(symbol, timeFrame, bars);

        // Act
        await _cacheService.PerformMaintenanceAsync(retainDays: 365);

        // Assert
        var remainingBars = await _dbContext.CachedStockBars.Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame).ToListAsync();
        remainingBars.Should().HaveCount(1);
        remainingBars[0].TimeUtc.Should().Be(recentDate);
    }

    [Fact]
    public async Task GetCacheStatsAsync_ShouldReturnStatistics()
    {
        // Arrange
        var symbol = "AAPL";
        var timeFrame = "Day";
        var bars = new List<IBar>
        {
            CreateMockBar("AAPL", new DateTime(2024, 1, 1), 150m, 155m, 148m, 152m, 1000000),
            CreateMockBar("AAPL", new DateTime(2024, 1, 2), 152m, 158m, 150m, 156m, 1100000)
        };

        await _cacheService.CacheBarsAsync(symbol, timeFrame, bars);

        // Act
        var stats = await _cacheService.GetCacheStatsAsync();

        // Assert
        var cacheKey = StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        stats.Should().ContainKey(cacheKey);
        stats[cacheKey].Symbol.Should().Be(symbol);
        stats[cacheKey].TimeFrame.Should().Be(timeFrame);
        stats[cacheKey].BarCount.Should().Be(2);
    }

    private static IBar CreateMockBar(string symbol, DateTime timeUtc, decimal open, decimal high, decimal low, decimal close, long volume)
    {
        var mock = new Mock<IBar>();
        mock.Setup(b => b.Symbol).Returns(symbol);
        mock.Setup(b => b.TimeUtc).Returns(timeUtc);
        mock.Setup(b => b.Open).Returns(open);
        mock.Setup(b => b.High).Returns(high);
        mock.Setup(b => b.Low).Returns(low);
        mock.Setup(b => b.Close).Returns(close);
        mock.Setup(b => b.Volume).Returns(volume);
        mock.Setup(b => b.Vwap).Returns(0);
        mock.Setup(b => b.TradeCount).Returns(0);
        return mock.Object;
    }
}
