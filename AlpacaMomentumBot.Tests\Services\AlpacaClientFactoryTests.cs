using AlpacaMomentumBot.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AlpacaMomentumBot.Tests.Services;

public class AlpacaClientFactoryTests : IDisposable
{
    private readonly Mock<ILogger<AlpacaClientFactory>> _mockLogger;
    private readonly Mock<ILogger<AlpacaRateLimitHelper>> _mockRateLimitLogger;
    private readonly AlpacaClientFactory _factory;

    public AlpacaClientFactoryTests()
    {
        _mockLogger = new Mock<ILogger<AlpacaClientFactory>>();
        _mockRateLimitLogger = new Mock<ILogger<AlpacaRateLimitHelper>>();

        // Set up environment variables for testing
        Environment.SetEnvironmentVariable("APCA_API_KEY_ID", "test-key-id");
        Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", "test-secret-key");
        Environment.SetEnvironmentVariable("APCA_API_ENV", "paper");

        _factory = new AlpacaClientFactory(_mockLogger.Object, _mockRateLimitLogger.Object);
    }

    [Fact]
    public void CreateTradingClient_WithValidEnvironmentVariables_ShouldReturnClient()
    {
        // Act
        var client = _factory.CreateTradingClient();

        // Assert
        client.Should().NotBeNull();
        client.Should().BeAssignableTo<IAlpacaTradingClient>();
    }

    [Fact]
    public void CreateDataClient_WithValidEnvironmentVariables_ShouldReturnClient()
    {
        // Act
        var client = _factory.CreateDataClient();

        // Assert
        client.Should().NotBeNull();
        client.Should().BeAssignableTo<IAlpacaDataClient>();
    }

    [Fact]
    public void CreateStreamingClient_WithValidEnvironmentVariables_ShouldReturnClient()
    {
        // Act
        var client = _factory.CreateStreamingClient();

        // Assert
        client.Should().NotBeNull();
        client.Should().BeAssignableTo<IAlpacaStreamingClient>();
    }

    [Fact]
    public void CreateDataStreamingClient_WithValidEnvironmentVariables_ShouldReturnClient()
    {
        // Act
        var client = _factory.CreateDataStreamingClient();

        // Assert
        client.Should().NotBeNull();
        client.Should().BeAssignableTo<IAlpacaDataStreamingClient>();
    }

    [Fact]
    public void CreateTradingClient_WithMissingApiKeyId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_KEY_ID", null);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _factory.CreateTradingClient());
    }

    [Fact]
    public void CreateTradingClient_WithMissingSecretKey_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", null);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _factory.CreateTradingClient());
    }

    [Fact]
    public void CreateDataClient_WithMissingApiKeyId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_KEY_ID", null);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _factory.CreateDataClient());
    }

    [Fact]
    public void CreateDataClient_WithMissingSecretKey_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", null);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _factory.CreateDataClient());
    }

    [Fact]
    public void CreateStreamingClient_WithMissingApiKeyId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_KEY_ID", null);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _factory.CreateStreamingClient());
    }

    [Fact]
    public void CreateStreamingClient_WithMissingSecretKey_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", null);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _factory.CreateStreamingClient());
    }

    [Fact]
    public void CreateDataStreamingClient_WithMissingApiKeyId_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_KEY_ID", null);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _factory.CreateDataStreamingClient());
    }

    [Fact]
    public void CreateDataStreamingClient_WithMissingSecretKey_ShouldThrowInvalidOperationException()
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", null);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => _factory.CreateDataStreamingClient());
    }

    [Theory]
    [InlineData("paper")]
    [InlineData("PAPER")]
    [InlineData("Paper")]
    public void CreateTradingClient_WithPaperEnvironment_ShouldLogPaperEnvironment(string environment)
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_ENV", environment);

        // Act
        var client = _factory.CreateTradingClient();

        // Assert
        client.Should().NotBeNull();
        // Verify that the logger was called with paper environment message
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("paper")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Theory]
    [InlineData("live")]
    [InlineData("LIVE")]
    [InlineData("Live")]
    [InlineData("production")]
    [InlineData(null)]
    [InlineData("")]
    public void CreateTradingClient_WithLiveEnvironment_ShouldLogLiveEnvironment(string? environment)
    {
        // Arrange
        Environment.SetEnvironmentVariable("APCA_API_ENV", environment);

        // Act
        var client = _factory.CreateTradingClient();

        // Assert
        client.Should().NotBeNull();
        // Verify that the logger was called with live environment message
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("live")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public void GetRateLimitHelper_ShouldReturnHelper()
    {
        // Act
        var helper = _factory.GetRateLimitHelper();

        // Assert
        helper.Should().NotBeNull();
        helper.Should().BeAssignableTo<IAlpacaRateLimitHelper>();
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        _factory.Dispose(); // Should not throw
    }

    [Fact]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Act & Assert
        _factory.Dispose();
        _factory.Dispose(); // Should not throw on second call
    }

    public void Dispose()
    {
        // Clean up environment variables
        Environment.SetEnvironmentVariable("APCA_API_KEY_ID", null);
        Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", null);
        Environment.SetEnvironmentVariable("APCA_API_ENV", null);
        
        _factory?.Dispose();
    }
}

public class AlpacaClientFactoryIntegrationTests : IDisposable
{
    public AlpacaClientFactoryIntegrationTests()
    {
        // Set up valid test environment variables
        Environment.SetEnvironmentVariable("APCA_API_KEY_ID", "test-key-id");
        Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", "test-secret-key");
        Environment.SetEnvironmentVariable("APCA_API_ENV", "paper");
    }

    [Fact(Skip = "Integration test - requires valid API credentials")]
    public void CreateMultipleClients_ShouldReturnDifferentInstances()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<AlpacaClientFactory>>();
        var mockRateLimitLogger = new Mock<ILogger<AlpacaRateLimitHelper>>();
        var factory = new AlpacaClientFactory(mockLogger.Object, mockRateLimitLogger.Object);

        // Act
        var tradingClient1 = factory.CreateTradingClient();
        var tradingClient2 = factory.CreateTradingClient();
        var dataClient1 = factory.CreateDataClient();
        var dataClient2 = factory.CreateDataClient();
        var streamingClient1 = factory.CreateStreamingClient();
        var streamingClient2 = factory.CreateStreamingClient();
        var dataStreamingClient1 = factory.CreateDataStreamingClient();
        var dataStreamingClient2 = factory.CreateDataStreamingClient();

        // Assert
        tradingClient1.Should().NotBeSameAs(tradingClient2);
        dataClient1.Should().NotBeSameAs(dataClient2);
        streamingClient1.Should().NotBeSameAs(streamingClient2);
        dataStreamingClient1.Should().NotBeSameAs(dataStreamingClient2);

        // Clean up
        factory.Dispose();
    }

    public void Dispose()
    {
        // Clean up environment variables
        Environment.SetEnvironmentVariable("APCA_API_KEY_ID", null);
        Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", null);
        Environment.SetEnvironmentVariable("APCA_API_ENV", null);
    }
}
