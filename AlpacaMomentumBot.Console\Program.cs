using AlpacaMomentumBot.Services;
using AlpacaMomentumBot.Console;
using AlpacaMomentumBot.Data;
using DotNetEnv;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using Serilog;

// Load environment variables from .env file
var envPath = ".env";
if (File.Exists(envPath))
{
    Env.Load(envPath);

    // Force reload by reading the file manually to ensure variables are set
    var lines = File.ReadAllLines(envPath);
    foreach (var line in lines)
    {
        if (line.StartsWith("#") || string.IsNullOrWhiteSpace(line)) continue;

        var parts = line.Split('=', 2);
        if (parts.Length == 2)
        {
            Environment.SetEnvironmentVariable(parts[0].Trim(), parts[1].Trim());
        }
    }
}

Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/alpaca-momentum-bot-.log",
                  rollingInterval: RollingInterval.Day,
                  retainedFileCountLimit: 30)
    .CreateLogger();

try
{
    // Check if user wants to check account status
    if (args.Length > 0 && args[0].Equals("--check-account", StringComparison.OrdinalIgnoreCase))
    {
        await AccountChecker.CheckAccountAsync();
        return;
    }

    // Check if user wants to perform cache maintenance
    if (args.Length > 0 && args[0].Equals("--cache-maintenance", StringComparison.OrdinalIgnoreCase))
    {
        await PerformCacheMaintenanceAsync(args);
        return;
    }

    // Check if user wants to view cache report
    if (args.Length > 0 && args[0].Equals("--cache-report", StringComparison.OrdinalIgnoreCase))
    {
        await ShowCacheReportAsync();
        return;
    }

    Log.Information("Alpaca Momentum Bot — manual one-shot run");

    using IHost host = Host.CreateDefaultBuilder(args)
        .UseSerilog()
        .ConfigureServices(services =>
        {
            // HTTP client factory for Polygon with rate limiting
            services.AddHttpClient();

            // Rate limiting policies
            services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

            // Configure Polygon HTTP client with Polly policies
            services.AddHttpClient("polygon", client =>
            {
                client.BaseAddress = new Uri("https://api.polygon.io/");
                client.Timeout = TimeSpan.FromSeconds(30);
            })
            .AddPolicyHandler((serviceProvider, request) =>
            {
                var policyFactory = serviceProvider.GetRequiredService<IRateLimitPolicyFactory>();
                return policyFactory.CreatePolygonPolicy();
            });

            // Database and caching
            services.AddDbContext<IndexCacheDbContext>(options =>
                options.UseSqlite("Data Source=index_cache.db"));
            services.AddScoped<IIndexCacheService, IndexCacheService>();

            services.AddDbContext<StockBarCacheDbContext>(options =>
                options.UseSqlite("Data Source=stock_cache.db"));
            services.AddScoped<IStockBarCacheService, StockBarCacheService>();
            services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();

            // infrastructure
            services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
            services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
            services.AddSingleton<IMarketDataService, MarketDataService>();
            services.AddSingleton<IStreamingDataService, StreamingDataService>();
            services.AddSingleton<IMarketSessionGuard, MarketSessionGuard>();
            services.AddSingleton<ITimeProvider, SystemTimeProvider>();
            services.AddSingleton<IUniverseProvider, FileUniverseProvider>();

            // strategy stack
            services.AddScoped<ISignalGenerator, SignalGenerator>();
            services.AddScoped<IRiskManager, RiskManager>();
            services.AddScoped<IPortfolioGate, PortfolioGate>();
            services.AddScoped<IStopManager, StopManager>();
            services.AddScoped<ITradeExecutor, TradeExecutor>();
            services.AddScoped<ITradingService, TradingService>();
        })
        .Build();

    using var scope   = host.Services.CreateScope();

    // Initialize the cache databases
    var indexCacheService = scope.ServiceProvider.GetRequiredService<IIndexCacheService>();
    await indexCacheService.InitializeCacheAsync();

    var stockCacheService = scope.ServiceProvider.GetRequiredService<IStockBarCacheService>();
    await stockCacheService.InitializeCacheAsync();

    var guard         = scope.ServiceProvider.GetRequiredService<IMarketSessionGuard>();

    if (!await guard.CanTradeNowAsync())
    {
        Log.Information("Exiting — {Reason}", guard.Reason);
        return;
    }

    var trader = scope.ServiceProvider.GetRequiredService<ITradingService>();
    await trader.ExecuteCycleAsync();

    Log.Information("Trading cycle completed ✓");
}
catch (Exception ex)
{
    Log.Fatal(ex, "Fatal error — bot aborted");
}
finally
{
    Log.CloseAndFlush();
}

static async Task PerformCacheMaintenanceAsync(string[] args)
{
    Log.Information("Performing cache maintenance");

    // Parse retention days from command line (default: 365)
    int retainDays = 365;
    if (args.Length > 1 && int.TryParse(args[1], out var parsedDays) && parsedDays > 0)
    {
        retainDays = parsedDays;
    }

    using IHost host = Host.CreateDefaultBuilder(args)
        .UseSerilog()
        .ConfigureServices(services =>
        {
            services.AddHttpClient();
            services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

            services.AddDbContext<IndexCacheDbContext>(options =>
                options.UseSqlite("Data Source=index_cache.db"));
            services.AddScoped<IIndexCacheService, IndexCacheService>();

            services.AddDbContext<StockBarCacheDbContext>(options =>
                options.UseSqlite("Data Source=stock_cache.db"));
            services.AddScoped<IStockBarCacheService, StockBarCacheService>();
            services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();
        })
        .Build();

    using var scope = host.Services.CreateScope();
    var maintenanceService = scope.ServiceProvider.GetRequiredService<ICacheMaintenanceService>();

    await maintenanceService.PerformMaintenanceAsync(retainDays);
    Log.Information("Cache maintenance completed successfully");
}

static async Task ShowCacheReportAsync()
{
    Log.Information("Generating cache report");

    using IHost host = Host.CreateDefaultBuilder()
        .UseSerilog()
        .ConfigureServices(services =>
        {
            services.AddHttpClient();
            services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

            services.AddDbContext<IndexCacheDbContext>(options =>
                options.UseSqlite("Data Source=index_cache.db"));
            services.AddScoped<IIndexCacheService, IndexCacheService>();

            services.AddDbContext<StockBarCacheDbContext>(options =>
                options.UseSqlite("Data Source=stock_cache.db"));
            services.AddScoped<IStockBarCacheService, StockBarCacheService>();
            services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();
        })
        .Build();

    using var scope = host.Services.CreateScope();
    var maintenanceService = scope.ServiceProvider.GetRequiredService<ICacheMaintenanceService>();

    var report = await maintenanceService.GetCacheReportAsync();

    Log.Information("=== Cache Report ===");
    Log.Information("Stock Cache Entries: {StockEntries}", report.StockCacheEntries);
    Log.Information("Total Stock Bars: {TotalBars:N0}", report.TotalStockBars);
    Log.Information("Stock Symbols: {SymbolCount}", report.StockSymbolCount);
    Log.Information("Average Bars per Symbol: {AvgBars:F1}", report.AverageBarsPerSymbol);
    Log.Information("Total Cache Size: {SizeMB:F1} MB", report.TotalSizeMB);
    Log.Information("Generated: {GeneratedAt:yyyy-MM-dd HH:mm:ss} UTC", report.GeneratedAt);
}
