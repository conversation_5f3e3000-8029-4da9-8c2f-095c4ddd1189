using Microsoft.EntityFrameworkCore;
using AlpacaMomentumBot.Models;
using Alpaca.Markets;

namespace AlpacaMomentumBot.Data;

/// <summary>
/// Entity Framework DbContext for SQLite stock bar caching.
/// Manages cached stock/ETF data to reduce API calls to Alpaca/Polygon.
/// Implements 1-year retention with efficient indexing.
/// </summary>
public class StockBarCacheDbContext : DbContext
{
    public StockBarCacheDbContext(DbContextOptions<StockBarCacheDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Cached stock/ETF bars from Alpaca/Polygon APIs
    /// </summary>
    public DbSet<CachedStockBar> CachedStockBars { get; set; } = null!;

    /// <summary>
    /// Metadata about cached data for each symbol and timeframe
    /// </summary>
    public DbSet<StockCacheMetadata> StockCacheMetadata { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure CachedStockBar
        modelBuilder.Entity<CachedStockBar>(entity =>
        {
            // Composite unique index for efficient queries and deduplication
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame, e.TimeUtc })
                  .IsUnique()
                  .HasDatabaseName("IX_CachedStockBars_Symbol_TimeFrame_TimeUtc");

            // Additional indexes for common query patterns
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame })
                  .HasDatabaseName("IX_CachedStockBars_Symbol_TimeFrame");

            entity.HasIndex(e => e.TimeUtc)
                  .HasDatabaseName("IX_CachedStockBars_TimeUtc");

            entity.HasIndex(e => e.CachedAt)
                  .HasDatabaseName("IX_CachedStockBars_CachedAt");
        });

        // Configure StockCacheMetadata
        modelBuilder.Entity<StockCacheMetadata>(entity =>
        {
            entity.HasKey(e => e.CacheKey);
            
            entity.HasIndex(e => new { e.Symbol, e.TimeFrame })
                  .HasDatabaseName("IX_StockCacheMetadata_Symbol_TimeFrame");
        });
    }

    /// <summary>
    /// Ensures the database is created and migrations are applied
    /// </summary>
    public async Task EnsureDatabaseCreatedAsync()
    {
        await Database.EnsureCreatedAsync();
    }

    /// <summary>
    /// Gets cached bars for a symbol and timeframe within a date range
    /// </summary>
    public async Task<List<CachedStockBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        return await CachedStockBars
            .Where(b => b.Symbol == symbol && b.TimeFrame == timeFrame && b.TimeUtc >= startDate && b.TimeUtc <= endDate)
            .OrderBy(b => b.TimeUtc)
            .ToListAsync();
    }

    /// <summary>
    /// Gets the latest cached date for a symbol and timeframe
    /// </summary>
    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame)
    {
        var cacheKey = StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await StockCacheMetadata.FindAsync(cacheKey);
        return metadata?.LatestDataDate;
    }

    /// <summary>
    /// Gets the earliest cached date for a symbol and timeframe
    /// </summary>
    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame)
    {
        var cacheKey = StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var metadata = await StockCacheMetadata.FindAsync(cacheKey);
        return metadata?.EarliestDataDate;
    }

    /// <summary>
    /// Adds or updates cached bars for a symbol and timeframe
    /// </summary>
    public async Task AddOrUpdateCachedBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        var cachedBars = bars.Select(bar => CachedStockBar.FromIBar(symbol, timeFrame, bar)).ToList();
        
        if (!cachedBars.Any())
            return;

        // Add new bars (EF will handle duplicates based on unique constraint)
        foreach (var bar in cachedBars)
        {
            var existing = await CachedStockBars
                .FirstOrDefaultAsync(b => b.Symbol == symbol && b.TimeFrame == timeFrame && b.TimeUtc == bar.TimeUtc);
            
            if (existing == null)
            {
                CachedStockBars.Add(bar);
            }
            else
            {
                // Update existing bar with latest data
                existing.Open = bar.Open;
                existing.High = bar.High;
                existing.Low = bar.Low;
                existing.Close = bar.Close;
                existing.Volume = bar.Volume;
                existing.Vwap = bar.Vwap;
                existing.TradeCount = bar.TradeCount;
                existing.CachedAt = DateTime.UtcNow;
            }
        }

        // Update metadata
        var cacheKey = StockCacheMetadata.CreateCacheKey(symbol, timeFrame);
        var latestDate = cachedBars.Max(b => b.TimeUtc);
        var earliestDate = cachedBars.Min(b => b.TimeUtc);
        var metadata = await StockCacheMetadata.FindAsync(cacheKey);
        
        if (metadata == null)
        {
            metadata = new StockCacheMetadata
            {
                CacheKey = cacheKey,
                Symbol = symbol,
                TimeFrame = timeFrame,
                LatestDataDate = latestDate,
                EarliestDataDate = earliestDate,
                LastUpdated = DateTime.UtcNow,
                BarCount = cachedBars.Count
            };
            StockCacheMetadata.Add(metadata);
        }
        else
        {
            // Update date ranges
            if (latestDate > metadata.LatestDataDate)
            {
                metadata.LatestDataDate = latestDate;
            }
            if (earliestDate < metadata.EarliestDataDate)
            {
                metadata.EarliestDataDate = earliestDate;
            }
            metadata.LastUpdated = DateTime.UtcNow;
            metadata.BarCount = await CachedStockBars.CountAsync(b => b.Symbol == symbol && b.TimeFrame == timeFrame);
        }

        await SaveChangesAsync();
    }

    /// <summary>
    /// Cleans up old cached data (older than specified days)
    /// </summary>
    public async Task CleanupOldDataAsync(int retainDays = 365)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);
        
        var oldBars = CachedStockBars.Where(b => b.TimeUtc < cutoffDate);
        CachedStockBars.RemoveRange(oldBars);
        
        // Update metadata after cleanup
        var affectedSymbols = await CachedStockBars
            .Select(b => new { b.Symbol, b.TimeFrame })
            .Distinct()
            .ToListAsync();

        foreach (var symbolTimeFrame in affectedSymbols)
        {
            var cacheKey = StockCacheMetadata.CreateCacheKey(symbolTimeFrame.Symbol, symbolTimeFrame.TimeFrame);
            var metadata = await StockCacheMetadata.FindAsync(cacheKey);
            
            if (metadata != null)
            {
                var remainingBars = await CachedStockBars
                    .Where(b => b.Symbol == symbolTimeFrame.Symbol && b.TimeFrame == symbolTimeFrame.TimeFrame)
                    .ToListAsync();

                if (remainingBars.Any())
                {
                    metadata.EarliestDataDate = remainingBars.Min(b => b.TimeUtc);
                    metadata.LatestDataDate = remainingBars.Max(b => b.TimeUtc);
                    metadata.BarCount = remainingBars.Count;
                    metadata.LastUpdated = DateTime.UtcNow;
                }
                else
                {
                    // No bars left, remove metadata
                    StockCacheMetadata.Remove(metadata);
                }
            }
        }
        
        await SaveChangesAsync();
    }

    /// <summary>
    /// Gets cache statistics for monitoring
    /// </summary>
    public async Task<IDictionary<string, Services.CacheStats>> GetCacheStatsAsync()
    {
        var stats = new Dictionary<string, Services.CacheStats>();

        var metadataList = await StockCacheMetadata.ToListAsync();

        foreach (var metadata in metadataList)
        {
            // Estimate size (rough calculation)
            var estimatedSizeBytes = metadata.BarCount * 100; // Rough estimate: 100 bytes per bar

            var cacheStats = new Services.CacheStats(
                metadata.Symbol,
                metadata.TimeFrame,
                metadata.BarCount,
                metadata.EarliestDataDate,
                metadata.LatestDataDate,
                metadata.LastUpdated,
                estimatedSizeBytes
            );

            stats[metadata.CacheKey] = cacheStats;
        }

        return stats;
    }
}
