using Alpaca.Markets;

namespace AlpacaMomentumBot.Services;

/// <summary>
/// Service for caching stock/ETF bar data in SQLite to reduce API calls to Alpaca/Polygon.
/// Implements 1-year retention strategy and only fetches missing data to minimize API usage.
/// </summary>
public interface IStockBarCacheService
{
    /// <summary>
    /// Gets cached stock bars for a symbol within the specified date range.
    /// Returns only the bars that are available in cache.
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol (e.g., "AAPL", "SPY")</param>
    /// <param name="timeFrame">Timeframe: "Day" or "Minute"</param>
    /// <param name="startDate">Start date for the range</param>
    /// <param name="endDate">End date for the range</param>
    /// <returns>Cached stock bars within the date range</returns>
    Task<IReadOnlyList<IBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate);

    /// <summary>
    /// Caches stock bars for a symbol and timeframe.
    /// Automatically handles deduplication and metadata updates.
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <param name="timeFrame">Timeframe: "Day" or "Minute"</param>
    /// <param name="bars">Bars to cache</param>
    Task CacheBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars);

    /// <summary>
    /// Determines the missing date range that needs to be fetched from API.
    /// Returns null if cache is up to date, otherwise returns the range to fetch.
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <param name="timeFrame">Timeframe: "Day" or "Minute"</param>
    /// <param name="requestedStartDate">Requested start date</param>
    /// <param name="requestedEndDate">Requested end date</param>
    /// <returns>Date range to fetch, or null if cache is sufficient</returns>
    Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, string timeFrame, DateTime requestedStartDate, DateTime requestedEndDate);

    /// <summary>
    /// Gets the latest cached date for a symbol and timeframe.
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <param name="timeFrame">Timeframe: "Day" or "Minute"</param>
    /// <returns>Latest cached date, or null if no data cached</returns>
    Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame);

    /// <summary>
    /// Gets the earliest cached date for a symbol and timeframe.
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <param name="timeFrame">Timeframe: "Day" or "Minute"</param>
    /// <returns>Earliest cached date, or null if no data cached</returns>
    Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame);

    /// <summary>
    /// Checks if the cache is fresh for the requested date range.
    /// </summary>
    /// <param name="symbol">Stock/ETF symbol</param>
    /// <param name="timeFrame">Timeframe: "Day" or "Minute"</param>
    /// <param name="requestedEndDate">Requested end date</param>
    /// <returns>True if cache has data up to the requested end date</returns>
    Task<bool> IsCacheFreshAsync(string symbol, string timeFrame, DateTime requestedEndDate);

    /// <summary>
    /// Performs cache maintenance by removing data older than the retention period.
    /// Default retention is 365 days (1 year).
    /// </summary>
    /// <param name="retainDays">Number of days to retain (default: 365)</param>
    Task PerformMaintenanceAsync(int retainDays = 365);

    /// <summary>
    /// Initializes the cache database and ensures tables are created.
    /// </summary>
    Task InitializeCacheAsync();

    /// <summary>
    /// Gets cache statistics for monitoring and debugging.
    /// </summary>
    /// <returns>Dictionary with cache statistics by symbol and timeframe</returns>
    Task<IDictionary<string, CacheStats>> GetCacheStatsAsync();
}

/// <summary>
/// Statistics about cached data for a symbol and timeframe
/// </summary>
public readonly record struct CacheStats(
    string Symbol,
    string TimeFrame,
    int BarCount,
    DateTime? EarliestDate,
    DateTime? LatestDate,
    DateTime LastUpdated,
    long SizeBytes
);
