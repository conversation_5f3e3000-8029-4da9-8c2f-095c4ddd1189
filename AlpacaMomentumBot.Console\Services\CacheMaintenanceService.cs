using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Services;

/// <summary>
/// Service for performing maintenance on all cache systems.
/// Handles cleanup of old data and optimization of cache databases.
/// </summary>
public interface ICacheMaintenanceService
{
    /// <summary>
    /// Performs maintenance on all cache systems.
    /// </summary>
    /// <param name="retainDays">Number of days to retain (default: 365)</param>
    Task PerformMaintenanceAsync(int retainDays = 365);

    /// <summary>
    /// Gets cache statistics for all cache systems.
    /// </summary>
    Task<CacheMaintenanceReport> GetCacheReportAsync();
}

/// <summary>
/// Implementation of cache maintenance service.
/// </summary>
public sealed class CacheMaintenanceService : ICacheMaintenanceService
{
    private readonly IIndexCacheService _indexCacheService;
    private readonly IStockBarCacheService _stockBarCacheService;
    private readonly ILogger<CacheMaintenanceService> _logger;

    public CacheMaintenanceService(
        IIndexCacheService indexCacheService,
        IStockBarCacheService stockBarCacheService,
        ILogger<CacheMaintenanceService> logger)
    {
        _indexCacheService = indexCacheService;
        _stockBarCacheService = stockBarCacheService;
        _logger = logger;
    }

    public async Task PerformMaintenanceAsync(int retainDays = 365)
    {
        _logger.LogInformation("Starting cache maintenance for all systems (retain {RetainDays} days)", retainDays);

        try
        {
            // Perform maintenance on index cache
            _logger.LogDebug("Performing index cache maintenance");
            await _indexCacheService.PerformMaintenanceAsync(retainDays);

            // Perform maintenance on stock bar cache
            _logger.LogDebug("Performing stock bar cache maintenance");
            await _stockBarCacheService.PerformMaintenanceAsync(retainDays);

            _logger.LogInformation("Cache maintenance completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            throw;
        }
    }

    public async Task<CacheMaintenanceReport> GetCacheReportAsync()
    {
        try
        {
            _logger.LogDebug("Generating cache maintenance report");

            // Get stock cache stats
            var stockCacheStats = await _stockBarCacheService.GetCacheStatsAsync();

            // Calculate totals
            var totalStockBars = stockCacheStats.Values.Sum(s => s.BarCount);
            var totalStockSizeBytes = stockCacheStats.Values.Sum(s => s.SizeBytes);
            var stockSymbolCount = stockCacheStats.Values.Select(s => s.Symbol).Distinct().Count();

            var report = new CacheMaintenanceReport(
                StockCacheEntries: stockCacheStats.Count,
                TotalStockBars: totalStockBars,
                StockSymbolCount: stockSymbolCount,
                TotalStockSizeBytes: totalStockSizeBytes,
                GeneratedAt: DateTime.UtcNow
            );

            _logger.LogInformation("Cache report generated: {StockEntries} stock cache entries, {TotalBars} total bars, {SizeMB:F1} MB",
                report.StockCacheEntries, report.TotalStockBars, report.TotalStockSizeBytes / 1024.0 / 1024.0);

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cache report");
            throw;
        }
    }
}

/// <summary>
/// Report containing cache maintenance statistics
/// </summary>
public readonly record struct CacheMaintenanceReport(
    int StockCacheEntries,
    int TotalStockBars,
    int StockSymbolCount,
    long TotalStockSizeBytes,
    DateTime GeneratedAt
)
{
    /// <summary>
    /// Total cache size in megabytes
    /// </summary>
    public double TotalSizeMB => TotalStockSizeBytes / 1024.0 / 1024.0;

    /// <summary>
    /// Average bars per symbol
    /// </summary>
    public double AverageBarsPerSymbol => StockSymbolCount > 0 ? (double)TotalStockBars / StockSymbolCount : 0;
};
